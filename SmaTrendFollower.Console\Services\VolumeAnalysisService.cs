using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for volume analysis and confirmation
/// </summary>
public interface IVolumeAnalysisService
{
    /// <summary>
    /// Analyzes volume patterns for signal confirmation
    /// </summary>
    VolumeAnalysis AnalyzeVolume(IEnumerable<IBar> bars);
    
    /// <summary>
    /// Checks if volume confirms the signal
    /// </summary>
    bool IsVolumeConfirming(VolumeAnalysis analysis);
}

/// <summary>
/// Volume analysis implementation
/// </summary>
public sealed class VolumeAnalysisService : IVolumeAnalysisService
{
    private readonly ILogger<VolumeAnalysisService> _logger;

    public VolumeAnalysisService(ILogger<VolumeAnalysisService> logger)
    {
        _logger = logger;
    }

    public VolumeAnalysis AnalyzeVolume(IEnumerable<IBar> bars)
    {
        var barsList = bars.ToList();
        if (barsList.Count < 20)
        {
            return new VolumeAnalysis(false, 0, 0, 0, 0, VolumePattern.Insufficient);
        }

        try
        {
            var volumes = barsList.Select(b => (decimal)b.Volume).ToList();
            var currentVolume = volumes.Last();
            
            // Calculate volume metrics
            var avgVolume20 = volumes.TakeLast(20).Average();
            var avgVolume50 = volumes.Count >= 50 ? volumes.TakeLast(50).Average() : avgVolume20;
            var volumeRatio = currentVolume / avgVolume20;
            
            // Volume trend analysis (last 5 days)
            var recentVolumes = volumes.TakeLast(5).ToList();
            var volumeTrend = CalculateVolumeTrend(recentVolumes);
            
            // Determine volume pattern
            var pattern = DetermineVolumePattern(currentVolume, avgVolume20, avgVolume50, volumeTrend);
            
            // Volume confirmation logic
            var isConfirming = IsVolumeConfirming(currentVolume, avgVolume20, pattern);
            
            _logger.LogDebug("Volume analysis: Current={Current:N0}, Avg20={Avg20:N0}, " +
                           "Ratio={Ratio:F2}, Pattern={Pattern}, Confirming={Confirming}",
                currentVolume, avgVolume20, volumeRatio, pattern, isConfirming);

            return new VolumeAnalysis(
                isConfirming,
                currentVolume,
                avgVolume20,
                avgVolume50,
                volumeRatio,
                pattern);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error analyzing volume");
            return new VolumeAnalysis(false, 0, 0, 0, 0, VolumePattern.Error);
        }
    }

    public bool IsVolumeConfirming(VolumeAnalysis analysis)
    {
        return analysis.IsConfirming;
    }

    private bool IsVolumeConfirming(decimal currentVolume, decimal avgVolume, VolumePattern pattern)
    {
        // Volume confirmation criteria
        var volumeRatio = currentVolume / avgVolume;
        
        return pattern switch
        {
            VolumePattern.BreakoutVolume => true,  // Strong confirmation
            VolumePattern.AboveAverage => volumeRatio >= 1.2m,  // 20% above average
            VolumePattern.Accumulation => volumeRatio >= 1.1m,  // 10% above average
            VolumePattern.Average => volumeRatio >= 0.8m,       // Not too low
            VolumePattern.BelowAverage => false,                // Poor confirmation
            VolumePattern.VeryLow => false,                     // No confirmation
            _ => false
        };
    }

    private decimal CalculateVolumeTrend(List<decimal> recentVolumes)
    {
        if (recentVolumes.Count < 3)
            return 0;

        // Simple linear trend calculation
        var n = recentVolumes.Count;
        var sumX = 0m;
        var sumY = 0m;
        var sumXY = 0m;
        var sumX2 = 0m;

        for (int i = 0; i < n; i++)
        {
            var x = i + 1;
            var y = recentVolumes[i];
            
            sumX += x;
            sumY += y;
            sumXY += x * y;
            sumX2 += x * x;
        }

        var slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        return slope;
    }

    private VolumePattern DetermineVolumePattern(
        decimal currentVolume, decimal avgVolume20, decimal avgVolume50, decimal trend)
    {
        var ratio20 = currentVolume / avgVolume20;
        var ratio50 = currentVolume / avgVolume50;

        // Breakout volume (very high)
        if (ratio20 >= 2.0m && ratio50 >= 1.5m)
            return VolumePattern.BreakoutVolume;

        // Above average with positive trend
        if (ratio20 >= 1.2m && trend > 0)
            return VolumePattern.Accumulation;

        // Above average
        if (ratio20 >= 1.2m)
            return VolumePattern.AboveAverage;

        // Average volume
        if (ratio20 >= 0.8m && ratio20 < 1.2m)
            return VolumePattern.Average;

        // Below average
        if (ratio20 >= 0.5m)
            return VolumePattern.BelowAverage;

        // Very low volume
        return VolumePattern.VeryLow;
    }
}

/// <summary>
/// Volume analysis result
/// </summary>
public record VolumeAnalysis(
    bool IsConfirming,
    decimal CurrentVolume,
    decimal AverageVolume20,
    decimal AverageVolume50,
    decimal VolumeRatio,
    VolumePattern Pattern);

/// <summary>
/// Volume pattern types
/// </summary>
public enum VolumePattern
{
    Insufficient,
    VeryLow,
    BelowAverage,
    Average,
    AboveAverage,
    Accumulation,
    BreakoutVolume,
    Error
}
