using SmaTrendFollower.Models;
using Microsoft.Extensions.Logging;
using System.Numerics;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Advanced portfolio optimization using Modern Portfolio Theory
/// </summary>
public interface IPortfolioOptimizer
{
    /// <summary>
    /// Optimizes portfolio allocation for given signals
    /// </summary>
    Task<PortfolioAllocation> OptimizePortfolioAsync(
        IEnumerable<TradingSignal> signals, 
        decimal totalCapital,
        PortfolioObjective objective = PortfolioObjective.MaxSharpe);
    
    /// <summary>
    /// Calculates portfolio risk metrics
    /// </summary>
    Task<PortfolioRiskMetrics> CalculateRiskMetricsAsync(PortfolioAllocation allocation);
    
    /// <summary>
    /// Rebalances existing portfolio based on new signals
    /// </summary>
    Task<RebalanceRecommendation> RebalancePortfolioAsync(
        PortfolioAllocation currentAllocation,
        IEnumerable<TradingSignal> newSignals);
}

/// <summary>
/// Portfolio optimizer implementation
/// </summary>
public sealed class PortfolioOptimizer : IPortfolioOptimizer
{
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<PortfolioOptimizer> _logger;
    
    // Optimization parameters
    private const int HistoricalDays = 252; // 1 year of trading days
    private const double RiskFreeRate = 0.05; // 5% annual risk-free rate
    private const int MaxIterations = 1000;
    private const double ConvergenceThreshold = 1e-6;

    public PortfolioOptimizer(
        IMarketDataService marketDataService,
        ILogger<PortfolioOptimizer> logger)
    {
        _marketDataService = marketDataService;
        _logger = logger;
    }

    public async Task<PortfolioAllocation> OptimizePortfolioAsync(
        IEnumerable<TradingSignal> signals, 
        decimal totalCapital,
        PortfolioObjective objective = PortfolioObjective.MaxSharpe)
    {
        var signalsList = signals.ToList();
        
        if (!signalsList.Any())
        {
            return new PortfolioAllocation(new List<PositionAllocation>(), 0, 0, 0);
        }

        try
        {
            _logger.LogInformation("Optimizing portfolio for {Count} signals with ${Capital:N0} capital",
                signalsList.Count, totalCapital);

            // Get historical data for all symbols
            var historicalData = await GetHistoricalDataAsync(signalsList);
            
            // Calculate expected returns and covariance matrix
            var expectedReturns = CalculateExpectedReturns(historicalData);
            var covarianceMatrix = CalculateCovarianceMatrix(historicalData);
            
            // Optimize portfolio weights
            var weights = OptimizeWeights(expectedReturns, covarianceMatrix, objective);
            
            // Create position allocations
            var allocations = CreatePositionAllocations(signalsList, weights, totalCapital);
            
            // Calculate portfolio metrics
            var expectedReturn = CalculatePortfolioReturn(expectedReturns, weights);
            var portfolioRisk = CalculatePortfolioRisk(covarianceMatrix, weights);
            var sharpeRatio = (expectedReturn - RiskFreeRate) / portfolioRisk;
            
            _logger.LogInformation("Portfolio optimization complete: Expected Return={Return:P2}, " +
                                 "Risk={Risk:P2}, Sharpe={Sharpe:F2}",
                expectedReturn, portfolioRisk, sharpeRatio);

            return new PortfolioAllocation(allocations, expectedReturn, portfolioRisk, sharpeRatio);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error optimizing portfolio");
            
            // Fallback to equal weight allocation
            return CreateEqualWeightAllocation(signalsList, totalCapital);
        }
    }

    public async Task<PortfolioRiskMetrics> CalculateRiskMetricsAsync(PortfolioAllocation allocation)
    {
        try
        {
            var symbols = allocation.Positions.Select(p => p.Symbol).ToList();
            var historicalData = await GetHistoricalDataForSymbolsAsync(symbols);
            
            // Calculate Value at Risk (VaR)
            var portfolioReturns = CalculatePortfolioReturns(allocation, historicalData);
            var var95 = CalculateVaR(portfolioReturns, 0.95);
            var var99 = CalculateVaR(portfolioReturns, 0.99);
            
            // Calculate Expected Shortfall (Conditional VaR)
            var expectedShortfall = CalculateExpectedShortfall(portfolioReturns, 0.95);
            
            // Calculate Maximum Drawdown
            var maxDrawdown = CalculateMaxDrawdown(portfolioReturns);
            
            // Calculate Beta (relative to SPY)
            var beta = await CalculatePortfolioBetaAsync(allocation);
            
            // Calculate correlation with market
            var marketCorrelation = await CalculateMarketCorrelationAsync(allocation);
            
            return new PortfolioRiskMetrics(
                var95, var99, expectedShortfall, maxDrawdown, 
                beta, marketCorrelation, allocation.Risk);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating portfolio risk metrics");
            return new PortfolioRiskMetrics(0, 0, 0, 0, 1.0, 0.5, allocation.Risk);
        }
    }

    public async Task<RebalanceRecommendation> RebalancePortfolioAsync(
        PortfolioAllocation currentAllocation,
        IEnumerable<TradingSignal> newSignals)
    {
        try
        {
            var newSignalsList = newSignals.ToList();
            var currentValue = currentAllocation.Positions.Sum(p => p.CurrentValue);
            
            // Optimize new portfolio
            var optimizedAllocation = await OptimizePortfolioAsync(newSignalsList, currentValue);
            
            // Calculate rebalancing trades
            var trades = CalculateRebalancingTrades(currentAllocation, optimizedAllocation);
            
            // Calculate rebalancing costs
            var rebalancingCost = CalculateRebalancingCost(trades);
            
            // Calculate expected benefit
            var expectedBenefit = optimizedAllocation.ExpectedReturn - currentAllocation.ExpectedReturn;
            
            // Determine if rebalancing is worthwhile
            var isWorthwhile = expectedBenefit > (double)rebalancingCost * 2; // 2x cost threshold
            
            return new RebalanceRecommendation(
                trades, rebalancingCost, expectedBenefit, isWorthwhile, optimizedAllocation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating rebalance recommendation");
            return new RebalanceRecommendation(
                new List<RebalanceTrade>(), 0, 0, false, currentAllocation);
        }
    }

    private async Task<Dictionary<string, List<double>>> GetHistoricalDataAsync(List<TradingSignal> signals)
    {
        var historicalData = new Dictionary<string, List<double>>();
        var endDate = DateTime.UtcNow;
        var startDate = endDate.AddDays(-HistoricalDays - 50); // Extra buffer for calculations
        
        foreach (var signal in signals)
        {
            try
            {
                var barsResponse = await _marketDataService.GetStockBarsAsync(signal.Symbol, startDate, endDate);
                var bars = barsResponse.Items.ToList();
                
                if (bars.Count >= HistoricalDays)
                {
                    var returns = CalculateDailyReturns(bars);
                    historicalData[signal.Symbol] = returns.TakeLast(HistoricalDays).ToList();
                }
                else
                {
                    _logger.LogWarning("Insufficient historical data for {Symbol}: {Count} bars",
                        signal.Symbol, bars.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error getting historical data for {Symbol}", signal.Symbol);
            }
        }
        
        return historicalData;
    }

    private async Task<Dictionary<string, List<double>>> GetHistoricalDataForSymbolsAsync(List<string> symbols)
    {
        var signals = symbols.Select(s => new TradingSignal(s, 100m, 1m, 0m)).ToList();
        return await GetHistoricalDataAsync(signals);
    }

    private List<double> CalculateDailyReturns(List<IBar> bars)
    {
        var returns = new List<double>();
        
        for (int i = 1; i < bars.Count; i++)
        {
            var returnValue = Math.Log((double)(bars[i].Close / bars[i - 1].Close));
            returns.Add(returnValue);
        }
        
        return returns;
    }

    private double[] CalculateExpectedReturns(Dictionary<string, List<double>> historicalData)
    {
        var symbols = historicalData.Keys.ToList();
        var expectedReturns = new double[symbols.Count];
        
        for (int i = 0; i < symbols.Count; i++)
        {
            var returns = historicalData[symbols[i]];
            expectedReturns[i] = returns.Average() * 252; // Annualized
        }
        
        return expectedReturns;
    }

    private double[,] CalculateCovarianceMatrix(Dictionary<string, List<double>> historicalData)
    {
        var symbols = historicalData.Keys.ToList();
        var n = symbols.Count;
        var covMatrix = new double[n, n];
        
        for (int i = 0; i < n; i++)
        {
            for (int j = 0; j < n; j++)
            {
                var returnsI = historicalData[symbols[i]];
                var returnsJ = historicalData[symbols[j]];
                
                var covariance = CalculateCovariance(returnsI, returnsJ) * 252; // Annualized
                covMatrix[i, j] = covariance;
            }
        }
        
        return covMatrix;
    }

    private double CalculateCovariance(List<double> returnsX, List<double> returnsY)
    {
        var meanX = returnsX.Average();
        var meanY = returnsY.Average();
        
        var covariance = 0.0;
        var count = Math.Min(returnsX.Count, returnsY.Count);
        
        for (int i = 0; i < count; i++)
        {
            covariance += (returnsX[i] - meanX) * (returnsY[i] - meanY);
        }
        
        return covariance / (count - 1);
    }

    private double[] OptimizeWeights(
        double[] expectedReturns, 
        double[,] covarianceMatrix, 
        PortfolioObjective objective)
    {
        var n = expectedReturns.Length;
        var weights = new double[n];
        
        // Initialize with equal weights
        for (int i = 0; i < n; i++)
        {
            weights[i] = 1.0 / n;
        }
        
        // Simple optimization using gradient ascent for Sharpe ratio
        if (objective == PortfolioObjective.MaxSharpe)
        {
            weights = OptimizeSharpeRatio(expectedReturns, covarianceMatrix, weights);
        }
        else if (objective == PortfolioObjective.MinRisk)
        {
            weights = OptimizeMinimumRisk(covarianceMatrix, weights);
        }
        
        // Ensure weights sum to 1 and are non-negative
        NormalizeWeights(weights);
        
        return weights;
    }

    private double[] OptimizeSharpeRatio(double[] expectedReturns, double[,] covarianceMatrix, double[] initialWeights)
    {
        var weights = (double[])initialWeights.Clone();
        const double learningRate = 0.01;
        
        for (int iter = 0; iter < MaxIterations; iter++)
        {
            var gradients = CalculateSharpeGradients(expectedReturns, covarianceMatrix, weights);
            
            // Update weights
            for (int i = 0; i < weights.Length; i++)
            {
                weights[i] += learningRate * gradients[i];
                weights[i] = Math.Max(0, weights[i]); // Non-negative constraint
            }
            
            NormalizeWeights(weights);
            
            // Check convergence
            if (gradients.All(g => Math.Abs(g) < ConvergenceThreshold))
                break;
        }
        
        return weights;
    }

    private double[] OptimizeMinimumRisk(double[,] covarianceMatrix, double[] initialWeights)
    {
        var n = initialWeights.Length;
        var weights = new double[n];
        
        // For minimum variance portfolio, use analytical solution
        // w = (C^-1 * 1) / (1^T * C^-1 * 1)
        
        // Simplified approach: equal weights (would implement matrix inversion in production)
        for (int i = 0; i < n; i++)
        {
            weights[i] = 1.0 / n;
        }
        
        return weights;
    }

    private double[] CalculateSharpeGradients(double[] expectedReturns, double[,] covarianceMatrix, double[] weights)
    {
        var n = weights.Length;
        var gradients = new double[n];
        
        var portfolioReturn = CalculatePortfolioReturn(expectedReturns, weights);
        var portfolioRisk = CalculatePortfolioRisk(covarianceMatrix, weights);
        var sharpeRatio = (portfolioReturn - RiskFreeRate) / portfolioRisk;
        
        for (int i = 0; i < n; i++)
        {
            // Numerical gradient approximation
            const double epsilon = 1e-6;
            var weightsPlus = (double[])weights.Clone();
            weightsPlus[i] += epsilon;
            NormalizeWeights(weightsPlus);
            
            var returnPlus = CalculatePortfolioReturn(expectedReturns, weightsPlus);
            var riskPlus = CalculatePortfolioRisk(covarianceMatrix, weightsPlus);
            var sharpePlus = (returnPlus - RiskFreeRate) / riskPlus;
            
            gradients[i] = (sharpePlus - sharpeRatio) / epsilon;
        }
        
        return gradients;
    }

    private void NormalizeWeights(double[] weights)
    {
        var sum = weights.Sum();
        if (sum > 0)
        {
            for (int i = 0; i < weights.Length; i++)
            {
                weights[i] /= sum;
            }
        }
    }

    private double CalculatePortfolioReturn(double[] expectedReturns, double[] weights)
    {
        double portfolioReturn = 0;
        for (int i = 0; i < expectedReturns.Length; i++)
        {
            portfolioReturn += expectedReturns[i] * weights[i];
        }
        return portfolioReturn;
    }

    private double CalculatePortfolioRisk(double[,] covarianceMatrix, double[] weights)
    {
        var n = weights.Length;
        double variance = 0;
        
        for (int i = 0; i < n; i++)
        {
            for (int j = 0; j < n; j++)
            {
                variance += weights[i] * weights[j] * covarianceMatrix[i, j];
            }
        }
        
        return Math.Sqrt(variance);
    }

    private List<PositionAllocation> CreatePositionAllocations(
        List<TradingSignal> signals, 
        double[] weights, 
        decimal totalCapital)
    {
        var allocations = new List<PositionAllocation>();
        
        for (int i = 0; i < signals.Count; i++)
        {
            var signal = signals[i];
            var weight = (decimal)weights[i];
            var allocationValue = totalCapital * weight;
            var quantity = Math.Floor(allocationValue / signal.Price);
            var actualValue = quantity * signal.Price;
            
            if (quantity > 0)
            {
                allocations.Add(new PositionAllocation(
                    signal.Symbol, quantity, signal.Price, actualValue, weight));
            }
        }
        
        return allocations;
    }

    private PortfolioAllocation CreateEqualWeightAllocation(List<TradingSignal> signals, decimal totalCapital)
    {
        var weight = 1.0m / signals.Count;
        var allocations = new List<PositionAllocation>();
        
        foreach (var signal in signals)
        {
            var allocationValue = totalCapital * weight;
            var quantity = Math.Floor(allocationValue / signal.Price);
            var actualValue = quantity * signal.Price;
            
            if (quantity > 0)
            {
                allocations.Add(new PositionAllocation(
                    signal.Symbol, quantity, signal.Price, actualValue, weight));
            }
        }
        
        return new PortfolioAllocation(allocations, 0.08, 0.15, 0.53); // Default metrics
    }

    private double CalculateVaR(List<double> returns, double confidence)
    {
        var sortedReturns = returns.OrderBy(r => r).ToList();
        var index = (int)((1 - confidence) * sortedReturns.Count);
        return -sortedReturns[Math.Max(0, index)]; // VaR is positive loss
    }

    private double CalculateExpectedShortfall(List<double> returns, double confidence)
    {
        var var95 = CalculateVaR(returns, confidence);
        var tailReturns = returns.Where(r => -r >= var95).ToList();
        return tailReturns.Any() ? -tailReturns.Average() : var95;
    }

    private double CalculateMaxDrawdown(List<double> returns)
    {
        var cumulativeReturns = new List<double> { 1.0 };
        
        foreach (var ret in returns)
        {
            cumulativeReturns.Add(cumulativeReturns.Last() * (1 + ret));
        }
        
        var maxDrawdown = 0.0;
        var peak = cumulativeReturns[0];
        
        foreach (var value in cumulativeReturns)
        {
            if (value > peak)
                peak = value;
            
            var drawdown = (peak - value) / peak;
            if (drawdown > maxDrawdown)
                maxDrawdown = drawdown;
        }
        
        return maxDrawdown;
    }

    private List<double> CalculatePortfolioReturns(
        PortfolioAllocation allocation, 
        Dictionary<string, List<double>> historicalData)
    {
        var portfolioReturns = new List<double>();
        var positions = allocation.Positions.ToDictionary(p => p.Symbol, p => (double)p.Weight);
        
        // Find common length
        var minLength = historicalData.Values.Min(v => v.Count);
        
        for (int i = 0; i < minLength; i++)
        {
            var portfolioReturn = 0.0;
            
            foreach (var position in positions)
            {
                if (historicalData.TryGetValue(position.Key, out var returns) && i < returns.Count)
                {
                    portfolioReturn += position.Value * returns[i];
                }
            }
            
            portfolioReturns.Add(portfolioReturn);
        }
        
        return portfolioReturns;
    }

    private async Task<double> CalculatePortfolioBetaAsync(PortfolioAllocation allocation)
    {
        // Simplified beta calculation - would implement proper regression in production
        return 1.0; // Market beta
    }

    private async Task<double> CalculateMarketCorrelationAsync(PortfolioAllocation allocation)
    {
        // Simplified correlation calculation
        return 0.7; // 70% correlation with market
    }

    private List<RebalanceTrade> CalculateRebalancingTrades(
        PortfolioAllocation current, 
        PortfolioAllocation target)
    {
        var trades = new List<RebalanceTrade>();
        var currentPositions = current.Positions.ToDictionary(p => p.Symbol, p => p);
        var targetPositions = target.Positions.ToDictionary(p => p.Symbol, p => p);
        
        // Calculate trades needed
        var allSymbols = currentPositions.Keys.Union(targetPositions.Keys).ToList();
        
        foreach (var symbol in allSymbols)
        {
            var currentQty = currentPositions.GetValueOrDefault(symbol)?.Quantity ?? 0;
            var targetQty = targetPositions.GetValueOrDefault(symbol)?.Quantity ?? 0;
            var difference = targetQty - currentQty;
            
            if (Math.Abs(difference) > 0)
            {
                var price = targetPositions.GetValueOrDefault(symbol)?.Price ?? 
                           currentPositions.GetValueOrDefault(symbol)?.Price ?? 0;
                
                trades.Add(new RebalanceTrade(symbol, difference, price));
            }
        }
        
        return trades;
    }

    private decimal CalculateRebalancingCost(List<RebalanceTrade> trades)
    {
        // Simplified cost calculation (commission + spread)
        const decimal commissionPerTrade = 0m; // Commission-free trading
        const decimal spreadCostPercent = 0.001m; // 0.1% spread cost
        
        var totalCost = trades.Count * commissionPerTrade;
        totalCost += trades.Sum(t => Math.Abs(t.Quantity) * t.Price * spreadCostPercent);
        
        return totalCost;
    }
}

/// <summary>
/// Portfolio optimization objective
/// </summary>
public enum PortfolioObjective
{
    MaxSharpe,
    MinRisk,
    MaxReturn
}

/// <summary>
/// Portfolio allocation result
/// </summary>
public record PortfolioAllocation(
    List<PositionAllocation> Positions,
    double ExpectedReturn,
    double Risk,
    double SharpeRatio);

/// <summary>
/// Individual position allocation
/// </summary>
public record PositionAllocation(
    string Symbol,
    decimal Quantity,
    decimal Price,
    decimal CurrentValue,
    decimal Weight);

/// <summary>
/// Portfolio risk metrics
/// </summary>
public record PortfolioRiskMetrics(
    double VaR95,
    double VaR99,
    double ExpectedShortfall,
    double MaxDrawdown,
    double Beta,
    double MarketCorrelation,
    double Volatility);

/// <summary>
/// Rebalancing recommendation
/// </summary>
public record RebalanceRecommendation(
    List<RebalanceTrade> Trades,
    decimal RebalancingCost,
    double ExpectedBenefit,
    bool IsWorthwhile,
    PortfolioAllocation TargetAllocation);

/// <summary>
/// Individual rebalancing trade
/// </summary>
public record RebalanceTrade(
    string Symbol,
    decimal Quantity, // Positive = buy, negative = sell
    decimal Price);
