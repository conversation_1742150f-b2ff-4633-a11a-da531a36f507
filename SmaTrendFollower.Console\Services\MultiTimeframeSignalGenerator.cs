using SmaTrendFollower.Console.Extensions;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced signal generator with multi-timeframe analysis
/// </summary>
public sealed class MultiTimeframeSignalGenerator : ISignalGenerator
{
    private readonly IMarketDataService _marketDataService;
    private readonly IUniverseProvider _universeProvider;
    private readonly ILogger<MultiTimeframeSignalGenerator> _logger;

    public MultiTimeframeSignalGenerator(
        IMarketDataService marketDataService,
        IUniverseProvider universeProvider,
        ILogger<MultiTimeframeSignalGenerator> logger)
    {
        _marketDataService = marketDataService;
        _universeProvider = universeProvider;
        _logger = logger;
    }

    public async Task<IEnumerable<TradingSignal>> RunAsync(int topN = 10)
    {
        try
        {
            var symbols = await _universeProvider.GetSymbolsAsync();
            var symbolList = symbols.ToList();

            _logger.LogInformation("Multi-timeframe screening {Count} symbols for trading signals", symbolList.Count);

            var signals = new List<MultiTimeframeSignal>();

            // Process symbols in batches
            const int batchSize = 10;
            for (int i = 0; i < symbolList.Count; i += batchSize)
            {
                var batch = symbolList.Skip(i).Take(batchSize);
                var batchSignals = await ProcessBatchWithMultiTimeframe(batch);
                signals.AddRange(batchSignals);
            }

            // Sort by confluence score (higher is better)
            var topSignals = signals
                .OrderByDescending(s => s.ConfluenceScore)
                .ThenByDescending(s => s.SixMonthReturn)
                .Take(topN)
                .Select(s => new TradingSignal(s.Symbol, s.Price, s.Atr, s.SixMonthReturn))
                .ToList();

            _logger.LogInformation("Generated {Count} multi-timeframe signals from {Total} symbols", 
                topSignals.Count, symbolList.Count);

            return topSignals;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating multi-timeframe signals");
            return Enumerable.Empty<TradingSignal>();
        }
    }

    private async Task<List<MultiTimeframeSignal>> ProcessBatchWithMultiTimeframe(IEnumerable<string> symbols)
    {
        var signals = new List<MultiTimeframeSignal>();

        foreach (var symbol in symbols)
        {
            try
            {
                var signal = await AnalyzeMultiTimeframe(symbol);
                if (signal != null)
                {
                    signals.Add(signal);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error analyzing {Symbol} for multi-timeframe signals", symbol);
            }
        }

        return signals;
    }

    private async Task<MultiTimeframeSignal?> AnalyzeMultiTimeframe(string symbol)
    {
        try
        {
            // Get data for different timeframes
            var startDate = DateTime.UtcNow.AddDays(-300);
            var endDate = DateTime.UtcNow;

            // Daily bars for main analysis
            var dailyResponse = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
            var dailyBars = dailyResponse.Items.ToList();

            if (dailyBars.Count < 200) // Need enough data for SMA200
                return null;

            // Get hourly bars for short-term trend
            var hourlyStartDate = DateTime.UtcNow.AddDays(-30);
            var hourlyResponse = await _marketDataService.GetStockMinuteBarsAsync(symbol, hourlyStartDate, endDate);
            var hourlyBars = hourlyResponse.Items.Where(b => b.TimeUtc.Minute == 0).ToList(); // Filter to hourly

            if (hourlyBars.Count < 50) // Need enough hourly data
                return null;

            // Calculate daily timeframe indicators
            var currentPrice = dailyBars.Last().Close;
            var dailySma50 = (decimal)dailyBars.GetSma50();
            var dailySma200 = (decimal)dailyBars.GetSma200();
            var atr14 = (decimal)dailyBars.GetAtr14();
            var sixMonthReturn = (decimal)dailyBars.GetTotalReturn(126);

            // Calculate hourly timeframe indicators
            var hourlySma20 = hourlyBars.Count >= 20 ? 
                (decimal)hourlyBars.TakeLast(20).Average(b => b.Close) : currentPrice;
            var hourlySma50 = hourlyBars.Count >= 50 ? 
                (decimal)hourlyBars.TakeLast(50).Average(b => b.Close) : currentPrice;

            // Calculate 15-minute proxy (using recent hourly data)
            var recent15MinProxy = hourlyBars.Count >= 4 ? 
                (decimal)hourlyBars.TakeLast(4).Average(b => b.Close) : currentPrice;

            // Multi-timeframe confluence scoring
            var confluenceScore = CalculateConfluenceScore(
                currentPrice, dailySma50, dailySma200, 
                hourlySma20, hourlySma50, recent15MinProxy);

            // Volume confirmation (using daily volume)
            var avgVolume = dailyBars.TakeLast(20).Average(b => (decimal)b.Volume);
            var currentVolume = (decimal)dailyBars.Last().Volume;
            var volumeConfirmation = currentVolume > avgVolume * 1.2m; // 20% above average

            // Volatility filter
            var volatilityRatio = atr14 / currentPrice;
            if (volatilityRatio > 0.03m) // Skip if too volatile
                return null;

            // Require minimum confluence score
            if (confluenceScore < 0.6m) // 60% confluence minimum
                return null;

            // Apply volume filter for higher quality signals
            if (confluenceScore < 0.8m && !volumeConfirmation)
                return null;

            _logger.LogDebug("Multi-timeframe signal for {Symbol}: Price=${Price:F2}, " +
                           "Confluence={Confluence:F2}, Volume={Volume}", 
                symbol, currentPrice, confluenceScore, volumeConfirmation);

            return new MultiTimeframeSignal(
                symbol, currentPrice, atr14, sixMonthReturn, 
                confluenceScore, volumeConfirmation);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error in multi-timeframe analysis for {Symbol}", symbol);
            return null;
        }
    }

    private decimal CalculateConfluenceScore(
        decimal currentPrice, decimal dailySma50, decimal dailySma200,
        decimal hourlySma20, decimal hourlySma50, decimal recent15MinProxy)
    {
        var score = 0m;
        var maxScore = 6m;

        // Daily timeframe signals (weight: 3/6)
        if (currentPrice > dailySma50) score += 1m;
        if (currentPrice > dailySma200) score += 1m;
        if (dailySma50 > dailySma200) score += 1m; // Trending up

        // Hourly timeframe signals (weight: 2/6)
        if (currentPrice > hourlySma20) score += 1m;
        if (currentPrice > hourlySma50) score += 1m;

        // Short-term momentum (weight: 1/6)
        if (currentPrice > recent15MinProxy) score += 1m;

        return score / maxScore;
    }
}

/// <summary>
/// Multi-timeframe signal with confluence scoring
/// </summary>
internal record MultiTimeframeSignal(
    string Symbol,
    decimal Price,
    decimal Atr,
    decimal SixMonthReturn,
    decimal ConfluenceScore,
    bool VolumeConfirmation);
