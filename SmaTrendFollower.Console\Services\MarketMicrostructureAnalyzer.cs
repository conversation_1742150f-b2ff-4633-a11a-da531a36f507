using SmaTrendFollower.Models;
using Alpaca.Markets;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Advanced market microstructure analysis for optimal execution
/// </summary>
public interface IMarketMicrostructureAnalyzer
{
    /// <summary>
    /// Analyzes market microstructure for optimal execution timing
    /// </summary>
    Task<MicrostructureAnalysis> AnalyzeMarketMicrostructureAsync(string symbol);
    
    /// <summary>
    /// Predicts optimal execution window
    /// </summary>
    Task<ExecutionWindow> PredictOptimalExecutionWindowAsync(string symbol, decimal quantity);
    
    /// <summary>
    /// Analyzes order book depth and liquidity
    /// </summary>
    Task<LiquidityAnalysis> AnalyzeLiquidityAsync(string symbol);
    
    /// <summary>
    /// Estimates market impact of trade
    /// </summary>
    Task<MarketImpactEstimate> EstimateMarketImpactAsync(string symbol, decimal quantity);
}

/// <summary>
/// Market microstructure analyzer implementation
/// </summary>
public sealed class MarketMicrostructureAnalyzer : IMarketMicrostructureAnalyzer
{
    private readonly IMarketDataService _marketDataService;
    private readonly IAlpacaClientFactory _clientFactory;
    private readonly ILogger<MarketMicrostructureAnalyzer> _logger;

    public MarketMicrostructureAnalyzer(
        IMarketDataService marketDataService,
        IAlpacaClientFactory clientFactory,
        ILogger<MarketMicrostructureAnalyzer> logger)
    {
        _marketDataService = marketDataService;
        _clientFactory = clientFactory;
        _logger = logger;
    }

    public async Task<MicrostructureAnalysis> AnalyzeMarketMicrostructureAsync(string symbol)
    {
        try
        {
            _logger.LogInformation("Analyzing market microstructure for {Symbol}", symbol);

            // Get recent minute-level data
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddHours(-6); // Last 6 hours
            
            var minuteBars = await _marketDataService.GetStockMinuteBarsAsync(symbol, startTime, endTime);
            var bars = minuteBars.Items.ToList();

            if (bars.Count < 30)
            {
                _logger.LogWarning("Insufficient data for microstructure analysis of {Symbol}", symbol);
                return CreateDefaultAnalysis(symbol);
            }

            // Analyze various microstructure components
            var spreadAnalysis = AnalyzeSpreadPatterns(bars);
            var volumeProfile = AnalyzeVolumeProfile(bars);
            var priceImpact = AnalyzePriceImpact(bars);
            var volatilityPattern = AnalyzeIntradayVolatilityPattern(bars);
            var liquidityPattern = AnalyzeLiquidityPattern(bars);
            var orderFlow = AnalyzeOrderFlow(bars);

            var analysis = new MicrostructureAnalysis(
                symbol,
                DateTime.UtcNow,
                spreadAnalysis,
                volumeProfile,
                priceImpact,
                volatilityPattern,
                liquidityPattern,
                orderFlow);

            _logger.LogInformation("Microstructure analysis complete for {Symbol}: " +
                                 "Avg Spread={Spread:F4}, Liquidity Score={Liquidity:F2}",
                symbol, spreadAnalysis.AverageSpread, liquidityPattern.LiquidityScore);

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing market microstructure for {Symbol}", symbol);
            return CreateDefaultAnalysis(symbol);
        }
    }

    public async Task<ExecutionWindow> PredictOptimalExecutionWindowAsync(string symbol, decimal quantity)
    {
        try
        {
            var microstructure = await AnalyzeMarketMicrostructureAsync(symbol);
            var liquidityAnalysis = await AnalyzeLiquidityAsync(symbol);
            var marketImpact = await EstimateMarketImpactAsync(symbol, quantity);

            // Determine optimal execution strategy based on analysis
            var strategy = DetermineExecutionStrategy(microstructure, liquidityAnalysis, marketImpact, quantity);
            
            // Predict optimal timing windows
            var windows = PredictOptimalWindows(microstructure, strategy);
            
            var optimalWindow = new ExecutionWindow(
                strategy,
                windows,
                marketImpact.TotalImpact,
                CalculateExecutionUrgency(microstructure, marketImpact));

            _logger.LogInformation("Optimal execution window predicted for {Symbol}: " +
                                 "Strategy={Strategy}, Impact={Impact:P2}, Urgency={Urgency}",
                symbol, strategy, marketImpact.TotalImpact, optimalWindow.Urgency);

            return optimalWindow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error predicting execution window for {Symbol}", symbol);
            return new ExecutionWindow(ExecutionStrategy.Market, new List<TimeWindow>(), 0, ExecutionUrgency.Normal);
        }
    }

    public async Task<LiquidityAnalysis> AnalyzeLiquidityAsync(string symbol)
    {
        try
        {
            // Get recent trading data
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddHours(-2);
            
            var minuteBars = await _marketDataService.GetStockMinuteBarsAsync(symbol, startTime, endTime);
            var bars = minuteBars.Items.ToList();

            if (!bars.Any())
            {
                return new LiquidityAnalysis(symbol, 0.5, 0, 0, LiquidityRegime.Normal);
            }

            // Calculate liquidity metrics
            var avgVolume = bars.Average(b => (double)b.Volume);
            var avgDollarVolume = bars.Average(b => (double)(b.Volume * b.Close));
            var volumeVariability = CalculateVolumeVariability(bars);
            var liquidityScore = CalculateLiquidityScore(avgVolume, avgDollarVolume, volumeVariability);
            var regime = DetermineLiquidityRegime(liquidityScore, volumeVariability);

            return new LiquidityAnalysis(symbol, liquidityScore, avgVolume, avgDollarVolume, regime);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing liquidity for {Symbol}", symbol);
            return new LiquidityAnalysis(symbol, 0.5, 0, 0, LiquidityRegime.Normal);
        }
    }

    public async Task<MarketImpactEstimate> EstimateMarketImpactAsync(string symbol, decimal quantity)
    {
        try
        {
            var liquidityAnalysis = await AnalyzeLiquidityAsync(symbol);
            var microstructure = await AnalyzeMarketMicrostructureAsync(symbol);

            // Get current price for impact calculation
            var currentPrice = microstructure.VolumeProfile.CurrentPrice;
            var tradeValue = quantity * currentPrice;

            // Calculate market impact using square-root law
            var avgDollarVolume = (decimal)liquidityAnalysis.AverageDollarVolume;
            var participationRate = avgDollarVolume > 0 ? tradeValue / avgDollarVolume : 0;
            
            // Square-root impact model: Impact = σ * sqrt(participation_rate)
            var volatility = (decimal)microstructure.VolatilityPattern.CurrentVolatility;
            var temporaryImpact = volatility * (decimal)Math.Sqrt((double)Math.Min(participationRate, 1.0m));
            var permanentImpact = temporaryImpact * 0.3m; // Assume 30% of temporary impact is permanent

            var totalImpact = temporaryImpact + permanentImpact;
            var impactCost = totalImpact * tradeValue;

            var riskLevel = DetermineImpactRiskLevel(participationRate, liquidityAnalysis.Regime);

            return new MarketImpactEstimate(
                symbol, quantity, totalImpact, temporaryImpact, permanentImpact, 
                impactCost, participationRate, riskLevel);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error estimating market impact for {Symbol}", symbol);
            return new MarketImpactEstimate(symbol, quantity, 0.001m, 0.001m, 0, 0, 0, ImpactRiskLevel.Low);
        }
    }

    private SpreadAnalysis AnalyzeSpreadPatterns(List<IBar> bars)
    {
        var spreads = new List<decimal>();
        
        foreach (var bar in bars)
        {
            var spread = bar.High - bar.Low;
            var relativeSpread = spread / bar.Close;
            spreads.Add(relativeSpread);
        }

        var avgSpread = spreads.Average();
        var spreadVolatility = CalculateStandardDeviation(spreads.Select(s => (double)s).ToList());
        var tightness = 1.0 / (double)avgSpread; // Higher tightness = lower spread

        return new SpreadAnalysis(avgSpread, (decimal)spreadVolatility, (decimal)tightness);
    }

    private VolumeProfile AnalyzeVolumeProfile(List<IBar> bars)
    {
        if (!bars.Any()) return new VolumeProfile(0, 0, 0, 0, MicrostructureVolumePattern.Normal);

        var totalVolume = bars.Sum(b => (double)b.Volume);
        var avgVolume = totalVolume / bars.Count;
        var currentVolume = (double)bars.Last().Volume;
        var currentPrice = bars.Last().Close;

        // Analyze volume distribution
        var volumePattern = AnalyzeVolumePattern(bars);

        return new VolumeProfile(totalVolume, avgVolume, currentVolume, currentPrice, volumePattern);
    }

    private PriceImpactAnalysis AnalyzePriceImpact(List<IBar> bars)
    {
        var impacts = new List<double>();
        
        for (int i = 1; i < bars.Count; i++)
        {
            var volumeRatio = (double)bars[i].Volume / (double)bars[i - 1].Volume;
            var priceChange = Math.Abs((double)(bars[i].Close - bars[i - 1].Close) / (double)bars[i - 1].Close);
            
            if (volumeRatio > 0)
            {
                var impact = priceChange / Math.Log(1 + volumeRatio);
                impacts.Add(impact);
            }
        }

        var avgImpact = impacts.Any() ? impacts.Average() : 0;
        var impactVolatility = impacts.Any() ? CalculateStandardDeviation(impacts) : 0;

        return new PriceImpactAnalysis(avgImpact, impactVolatility);
    }

    private VolatilityPattern AnalyzeIntradayVolatilityPattern(List<IBar> bars)
    {
        var returns = new List<double>();
        
        for (int i = 1; i < bars.Count; i++)
        {
            var ret = Math.Log((double)(bars[i].Close / bars[i - 1].Close));
            returns.Add(ret);
        }

        var currentVolatility = returns.Any() ? CalculateStandardDeviation(returns) * Math.Sqrt(252) : 0;
        var avgVolatility = currentVolatility; // Simplified
        var volatilityTrend = CalculateVolatilityTrend(returns);

        return new VolatilityPattern(currentVolatility, avgVolatility, volatilityTrend);
    }

    private LiquidityPattern AnalyzeLiquidityPattern(List<IBar> bars)
    {
        var liquidityScores = new List<double>();
        
        foreach (var bar in bars)
        {
            var dollarVolume = (double)(bar.Volume * bar.Close);
            var spread = (double)(bar.High - bar.Low) / (double)bar.Close;
            
            // Simple liquidity score: higher volume and lower spread = higher liquidity
            var liquidityScore = dollarVolume / (1 + spread * 1000);
            liquidityScores.Add(liquidityScore);
        }

        var avgLiquidity = liquidityScores.Average();
        var normalizedScore = Math.Min(1.0, avgLiquidity / 1000000); // Normalize to 0-1

        return new LiquidityPattern(normalizedScore);
    }

    private OrderFlowAnalysis AnalyzeOrderFlow(List<IBar> bars)
    {
        // Simplified order flow analysis using price and volume
        var buyPressure = 0.0;
        var sellPressure = 0.0;
        
        for (int i = 1; i < bars.Count; i++)
        {
            var priceChange = (double)(bars[i].Close - bars[i - 1].Close);
            var volume = (double)bars[i].Volume;
            
            if (priceChange > 0)
                buyPressure += volume;
            else if (priceChange < 0)
                sellPressure += volume;
        }

        var totalFlow = buyPressure + sellPressure;
        var netFlow = totalFlow > 0 ? (buyPressure - sellPressure) / totalFlow : 0;
        var flowImbalance = Math.Abs(netFlow);

        return new OrderFlowAnalysis(buyPressure, sellPressure, netFlow, flowImbalance);
    }

    private MicrostructureVolumePattern AnalyzeVolumePattern(List<IBar> bars)
    {
        if (bars.Count < 5) return MicrostructureVolumePattern.Normal;

        var recentVolume = bars.TakeLast(5).Average(b => (double)b.Volume);
        var avgVolume = bars.Average(b => (double)b.Volume);
        var ratio = recentVolume / avgVolume;

        return ratio switch
        {
            > 2.0 => MicrostructureVolumePattern.Surge,
            > 1.5 => MicrostructureVolumePattern.High,
            < 0.5 => MicrostructureVolumePattern.Low,
            _ => MicrostructureVolumePattern.Normal
        };
    }

    private double CalculateStandardDeviation(List<double> values)
    {
        if (values.Count < 2) return 0;
        
        var mean = values.Average();
        var variance = values.Average(v => Math.Pow(v - mean, 2));
        return Math.Sqrt(variance);
    }

    private double CalculateVolumeVariability(List<IBar> bars)
    {
        var volumes = bars.Select(b => (double)b.Volume).ToList();
        return CalculateStandardDeviation(volumes) / volumes.Average();
    }

    private double CalculateLiquidityScore(double avgVolume, double avgDollarVolume, double volumeVariability)
    {
        // Combine volume metrics into liquidity score
        var volumeScore = Math.Min(1.0, avgVolume / 1000000); // Normalize volume
        var dollarVolumeScore = Math.Min(1.0, avgDollarVolume / 100000000); // Normalize dollar volume
        var stabilityScore = Math.Max(0, 1 - volumeVariability); // Lower variability = higher stability
        
        return (volumeScore + dollarVolumeScore + stabilityScore) / 3.0;
    }

    private LiquidityRegime DetermineLiquidityRegime(double liquidityScore, double volumeVariability)
    {
        return (liquidityScore, volumeVariability) switch
        {
            (> 0.8, < 0.3) => LiquidityRegime.High,
            (< 0.3, _) => LiquidityRegime.Low,
            (_, > 0.7) => LiquidityRegime.Volatile,
            _ => LiquidityRegime.Normal
        };
    }

    private double CalculateVolatilityTrend(List<double> returns)
    {
        if (returns.Count < 10) return 0;
        
        var recent = returns.TakeLast(5).ToList();
        var older = returns.Skip(returns.Count - 10).Take(5).ToList();
        
        var recentVol = CalculateStandardDeviation(recent);
        var olderVol = CalculateStandardDeviation(older);
        
        return olderVol > 0 ? (recentVol - olderVol) / olderVol : 0;
    }

    private ExecutionStrategy DetermineExecutionStrategy(
        MicrostructureAnalysis microstructure,
        LiquidityAnalysis liquidity,
        MarketImpactEstimate impact,
        decimal quantity)
    {
        // Determine strategy based on market conditions and trade size
        return (impact.RiskLevel, liquidity.Regime) switch
        {
            (ImpactRiskLevel.High, _) => ExecutionStrategy.TWAP,
            (ImpactRiskLevel.Medium, LiquidityRegime.Low) => ExecutionStrategy.VWAP,
            (_, LiquidityRegime.High) => ExecutionStrategy.Aggressive,
            _ => ExecutionStrategy.Balanced
        };
    }

    private List<TimeWindow> PredictOptimalWindows(MicrostructureAnalysis microstructure, ExecutionStrategy strategy)
    {
        var windows = new List<TimeWindow>();
        var now = DateTime.Now;
        
        // Predict optimal execution windows based on historical patterns
        // This is simplified - would use ML models in production
        
        if (strategy == ExecutionStrategy.Aggressive)
        {
            // Execute immediately
            windows.Add(new TimeWindow(now, now.AddMinutes(5), 0.9));
        }
        else if (strategy == ExecutionStrategy.TWAP)
        {
            // Spread over longer period
            for (int i = 0; i < 6; i++)
            {
                var start = now.AddMinutes(i * 10);
                var end = start.AddMinutes(10);
                windows.Add(new TimeWindow(start, end, 0.7 - i * 0.1));
            }
        }
        else
        {
            // Balanced approach
            windows.Add(new TimeWindow(now.AddMinutes(5), now.AddMinutes(15), 0.8));
            windows.Add(new TimeWindow(now.AddMinutes(20), now.AddMinutes(30), 0.6));
        }
        
        return windows;
    }

    private ExecutionUrgency CalculateExecutionUrgency(MicrostructureAnalysis microstructure, MarketImpactEstimate impact)
    {
        var volatility = microstructure.VolatilityPattern.CurrentVolatility;
        var impactLevel = impact.RiskLevel;
        
        return (volatility, impactLevel) switch
        {
            (> 0.3, ImpactRiskLevel.High) => ExecutionUrgency.High,
            (> 0.2, ImpactRiskLevel.Medium) => ExecutionUrgency.Medium,
            (< 0.1, ImpactRiskLevel.Low) => ExecutionUrgency.Low,
            _ => ExecutionUrgency.Normal
        };
    }

    private ImpactRiskLevel DetermineImpactRiskLevel(decimal participationRate, LiquidityRegime regime)
    {
        return (participationRate, regime) switch
        {
            (> 0.2m, _) => ImpactRiskLevel.High,
            (> 0.1m, LiquidityRegime.Low) => ImpactRiskLevel.High,
            (> 0.05m, _) => ImpactRiskLevel.Medium,
            _ => ImpactRiskLevel.Low
        };
    }

    private MicrostructureAnalysis CreateDefaultAnalysis(string symbol)
    {
        return new MicrostructureAnalysis(
            symbol,
            DateTime.UtcNow,
            new SpreadAnalysis(0.001m, 0.0005m, 1000m),
            new VolumeProfile(1000000, 100000, 100000, 100m, MicrostructureVolumePattern.Normal),
            new PriceImpactAnalysis(0.001, 0.0005),
            new VolatilityPattern(0.2, 0.2, 0),
            new LiquidityPattern(0.5),
            new OrderFlowAnalysis(500000, 500000, 0, 0));
    }
}

// Data structures for microstructure analysis
public record MicrostructureAnalysis(
    string Symbol,
    DateTime Timestamp,
    SpreadAnalysis SpreadAnalysis,
    VolumeProfile VolumeProfile,
    PriceImpactAnalysis PriceImpact,
    VolatilityPattern VolatilityPattern,
    LiquidityPattern LiquidityPattern,
    OrderFlowAnalysis OrderFlow);

public record SpreadAnalysis(decimal AverageSpread, decimal SpreadVolatility, decimal Tightness);

public record VolumeProfile(double TotalVolume, double AverageVolume, double CurrentVolume, decimal CurrentPrice, MicrostructureVolumePattern Pattern);

public record PriceImpactAnalysis(double AverageImpact, double ImpactVolatility);

public record VolatilityPattern(double CurrentVolatility, double AverageVolatility, double VolatilityTrend);

public record LiquidityPattern(double LiquidityScore);

public record OrderFlowAnalysis(double BuyPressure, double SellPressure, double NetFlow, double FlowImbalance);

public record ExecutionWindow(ExecutionStrategy Strategy, List<TimeWindow> OptimalWindows, decimal EstimatedImpact, ExecutionUrgency Urgency);

public record TimeWindow(DateTime Start, DateTime End, double Score);

public record LiquidityAnalysis(string Symbol, double LiquidityScore, double AverageVolume, double AverageDollarVolume, LiquidityRegime Regime);

public record MarketImpactEstimate(
    string Symbol, decimal Quantity, decimal TotalImpact, decimal TemporaryImpact, 
    decimal PermanentImpact, decimal ImpactCost, decimal ParticipationRate, ImpactRiskLevel RiskLevel);

public enum MicrostructureVolumePattern { Low, Normal, High, Surge }
public enum ExecutionStrategy { Market, Aggressive, Balanced, VWAP, TWAP }
public enum ExecutionUrgency { Low, Normal, Medium, High }
public enum LiquidityRegime { Low, Normal, High, Volatile }
public enum ImpactRiskLevel { Low, Medium, High }
