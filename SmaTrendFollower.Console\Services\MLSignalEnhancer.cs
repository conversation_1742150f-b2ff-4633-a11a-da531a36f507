using SmaTrendFollower.Models;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using Alpaca.Markets;

namespace SmaTrendFollower.Services;

/// <summary>
/// Machine Learning-based signal enhancement and scoring
/// </summary>
public interface IMLSignalEnhancer
{
    /// <summary>
    /// Enhances signals with ML-based scoring
    /// </summary>
    Task<IEnumerable<MLEnhancedTradingSignal>> EnhanceSignalsAsync(IEnumerable<TradingSignal> signals);
    
    /// <summary>
    /// Trains the model with historical performance data
    /// </summary>
    Task TrainModelAsync(IEnumerable<HistoricalTrade> trades);
    
    /// <summary>
    /// Gets model performance metrics
    /// </summary>
    Task<MLModelMetrics> GetModelMetricsAsync();
}

/// <summary>
/// ML Signal Enhancer implementation using ensemble methods
/// </summary>
public sealed class MLSignalEnhancer : IMLSignalEnhancer
{
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<MLSignalEnhancer> _logger;
    private readonly string _modelPath;
    
    // Feature weights (learned from historical data)
    private readonly Dictionary<string, double> _featureWeights;
    private readonly MLModelState _modelState;

    public MLSignalEnhancer(
        IMarketDataService marketDataService,
        ILogger<MLSignalEnhancer> logger)
    {
        _marketDataService = marketDataService;
        _logger = logger;
        _modelPath = Path.Combine(Environment.CurrentDirectory, "ml_model.json");
        
        // Initialize with default feature weights
        _featureWeights = new Dictionary<string, double>
        {
            ["momentum_score"] = 0.25,
            ["volume_score"] = 0.20,
            ["volatility_score"] = 0.15,
            ["trend_strength"] = 0.20,
            ["market_regime_score"] = 0.10,
            ["sector_rotation_score"] = 0.10
        };
        
        _modelState = LoadOrCreateModel();
    }

    public async Task<IEnumerable<MLEnhancedTradingSignal>> EnhanceSignalsAsync(IEnumerable<TradingSignal> signals)
    {
        var enhancedSignals = new List<MLEnhancedTradingSignal>();
        
        foreach (var signal in signals)
        {
            try
            {
                var features = await ExtractFeaturesAsync(signal);
                var mlScore = CalculateMLScore(features);
                var confidence = CalculateConfidence(features);
                var riskAdjustment = CalculateRiskAdjustment(features);
                
                var enhancedSignal = new MLEnhancedTradingSignal(
                    signal.Symbol,
                    signal.Price,
                    signal.Atr,
                    signal.SixMonthReturn,
                    mlScore,
                    confidence,
                    riskAdjustment,
                    features);
                
                enhancedSignals.Add(enhancedSignal);
                
                _logger.LogDebug("Enhanced signal for {Symbol}: ML Score={Score:F3}, " +
                               "Confidence={Confidence:F3}, Risk Adj={Risk:F3}",
                    signal.Symbol, mlScore, confidence, riskAdjustment);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error enhancing signal for {Symbol}", signal.Symbol);
                
                // Fallback to basic signal
                enhancedSignals.Add(new MLEnhancedTradingSignal(
                    signal.Symbol, signal.Price, signal.Atr, signal.SixMonthReturn,
                    0.5, 0.5, 1.0, new Dictionary<string, double>()));
            }
        }
        
        // Sort by ML score and return top signals
        return enhancedSignals
            .OrderByDescending(s => s.MLScore * s.Confidence)
            .ToList();
    }

    public async Task TrainModelAsync(IEnumerable<HistoricalTrade> trades)
    {
        try
        {
            _logger.LogInformation("Training ML model with {Count} historical trades", trades.Count());
            
            var trainingData = new List<TrainingExample>();
            
            foreach (var trade in trades)
            {
                var features = await ExtractHistoricalFeaturesAsync(trade);
                var outcome = CalculateTradeOutcome(trade);
                
                trainingData.Add(new TrainingExample(features, outcome));
            }
            
            // Simple gradient descent for feature weight optimization
            OptimizeFeatureWeights(trainingData);
            
            // Update model state
            _modelState.LastTrainingDate = DateTime.UtcNow;
            _modelState.TrainingDataCount = trainingData.Count;
            _modelState.ModelVersion++;
            
            // Save updated model
            SaveModel();
            
            _logger.LogInformation("ML model training completed. Version: {Version}, " +
                                 "Training samples: {Count}",
                _modelState.ModelVersion, trainingData.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error training ML model");
        }
    }

    public async Task<MLModelMetrics> GetModelMetricsAsync()
    {
        return new MLModelMetrics(
            _modelState.ModelVersion,
            _modelState.LastTrainingDate,
            _modelState.TrainingDataCount,
            CalculateModelAccuracy(),
            _featureWeights.ToDictionary(kv => kv.Key, kv => kv.Value));
    }

    private async Task<Dictionary<string, double>> ExtractFeaturesAsync(TradingSignal signal)
    {
        var features = new Dictionary<string, double>();
        
        try
        {
            // Get historical data for feature extraction
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddDays(-60);
            
            var barsResponse = await _marketDataService.GetStockBarsAsync(signal.Symbol, startDate, endDate);
            var bars = barsResponse.Items.ToList();
            
            if (bars.Count < 20)
            {
                return GetDefaultFeatures();
            }
            
            // Extract technical features
            features["momentum_score"] = CalculateMomentumScore(bars);
            features["volume_score"] = CalculateVolumeScore(bars);
            features["volatility_score"] = CalculateVolatilityScore(bars, signal.Atr);
            features["trend_strength"] = CalculateTrendStrength(bars);
            features["market_regime_score"] = 0.6; // Placeholder - would integrate with regime service
            features["sector_rotation_score"] = 0.5; // Placeholder - would integrate with sector analysis
            
            // Additional features
            features["rsi"] = CalculateRSI(bars);
            features["price_position"] = CalculatePricePosition(bars);
            features["volume_trend"] = CalculateVolumeTrend(bars);
            
            return features;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error extracting features for {Symbol}", signal.Symbol);
            return GetDefaultFeatures();
        }
    }

    private double CalculateMLScore(Dictionary<string, double> features)
    {
        double score = 0.0;
        double totalWeight = 0.0;
        
        foreach (var feature in features)
        {
            if (_featureWeights.TryGetValue(feature.Key, out var weight))
            {
                score += feature.Value * weight;
                totalWeight += weight;
            }
        }
        
        return totalWeight > 0 ? Math.Max(0, Math.Min(1, score / totalWeight)) : 0.5;
    }

    private double CalculateConfidence(Dictionary<string, double> features)
    {
        // Calculate confidence based on feature consistency
        var scores = features.Values.ToList();
        if (!scores.Any()) return 0.5;
        
        var mean = scores.Average();
        var variance = scores.Average(s => Math.Pow(s - mean, 2));
        var consistency = Math.Max(0, 1 - variance);
        
        return Math.Max(0.1, Math.Min(1.0, consistency));
    }

    private double CalculateRiskAdjustment(Dictionary<string, double> features)
    {
        // Adjust risk based on volatility and market conditions
        var volatilityScore = features.GetValueOrDefault("volatility_score", 0.5);
        var marketScore = features.GetValueOrDefault("market_regime_score", 0.5);
        
        // Higher volatility = lower risk adjustment (smaller positions)
        // Better market conditions = higher risk adjustment (larger positions)
        return Math.Max(0.5, Math.Min(1.5, marketScore * (2 - volatilityScore)));
    }

    private double CalculateMomentumScore(List<IBar> bars)
    {
        if (bars.Count < 10) return 0.5;
        
        var recent = bars.TakeLast(5).Average(b => b.Close);
        var older = bars.Skip(bars.Count - 15).Take(5).Average(b => b.Close);
        
        var momentum = (recent - older) / older;
        return Math.Max(0, Math.Min(1, 0.5 + (double)momentum * 10)); // Scale momentum
    }

    private double CalculateVolumeScore(List<IBar> bars)
    {
        if (bars.Count < 10) return 0.5;
        
        var recentVolume = bars.TakeLast(5).Average(b => (double)b.Volume);
        var avgVolume = bars.Average(b => (double)b.Volume);
        
        var volumeRatio = recentVolume / avgVolume;
        return Math.Max(0, Math.Min(1, volumeRatio / 2)); // Normalize to 0-1
    }

    private double CalculateVolatilityScore(List<IBar> bars, decimal atr)
    {
        if (bars.Count < 10) return 0.5;
        
        var currentPrice = bars.Last().Close;
        var volatilityRatio = (double)(atr / currentPrice);
        
        // Lower volatility gets higher score
        return Math.Max(0, Math.Min(1, 1 - volatilityRatio * 20));
    }

    private double CalculateTrendStrength(List<IBar> bars)
    {
        if (bars.Count < 20) return 0.5;
        
        var sma20 = bars.TakeLast(20).Average(b => b.Close);
        var sma50 = bars.Count >= 50 ? bars.TakeLast(50).Average(b => b.Close) : sma20;
        
        var trendStrength = Math.Abs(sma20 - sma50) / sma50;
        return Math.Max(0, Math.Min(1, (double)trendStrength * 50)); // Scale trend strength
    }

    private double CalculateRSI(List<IBar> bars)
    {
        if (bars.Count < 15) return 0.5;
        
        var gains = new List<double>();
        var losses = new List<double>();
        
        for (int i = 1; i < bars.Count; i++)
        {
            var change = bars[i].Close - bars[i - 1].Close;
            if (change > 0)
            {
                gains.Add((double)change);
                losses.Add(0);
            }
            else
            {
                gains.Add(0);
                losses.Add((double)Math.Abs(change));
            }
        }
        
        var avgGain = gains.TakeLast(14).Average();
        var avgLoss = losses.TakeLast(14).Average();
        
        if (avgLoss == 0) return 1.0;
        
        var rs = avgGain / avgLoss;
        var rsi = 100 - (100 / (1 + rs));
        
        return rsi / 100.0; // Normalize to 0-1
    }

    private double CalculatePricePosition(List<IBar> bars)
    {
        if (bars.Count < 20) return 0.5;
        
        var currentPrice = bars.Last().Close;
        var high20 = bars.TakeLast(20).Max(b => b.High);
        var low20 = bars.TakeLast(20).Min(b => b.Low);
        
        if (high20 == low20) return 0.5;
        
        return (double)((currentPrice - low20) / (high20 - low20));
    }

    private double CalculateVolumeTrend(List<IBar> bars)
    {
        if (bars.Count < 10) return 0.5;
        
        var recentVolume = bars.TakeLast(5).Average(b => (double)b.Volume);
        var olderVolume = bars.Skip(bars.Count - 10).Take(5).Average(b => (double)b.Volume);
        
        if (olderVolume == 0) return 0.5;
        
        var volumeTrend = (recentVolume - olderVolume) / olderVolume;
        return Math.Max(0, Math.Min(1, 0.5 + (double)volumeTrend));
    }

    private Dictionary<string, double> GetDefaultFeatures()
    {
        return new Dictionary<string, double>
        {
            ["momentum_score"] = 0.5,
            ["volume_score"] = 0.5,
            ["volatility_score"] = 0.5,
            ["trend_strength"] = 0.5,
            ["market_regime_score"] = 0.5,
            ["sector_rotation_score"] = 0.5,
            ["rsi"] = 0.5,
            ["price_position"] = 0.5,
            ["volume_trend"] = 0.5
        };
    }

    private async Task<Dictionary<string, double>> ExtractHistoricalFeaturesAsync(HistoricalTrade trade)
    {
        // Simplified - in production would extract features from historical data at trade time
        return GetDefaultFeatures();
    }

    private double CalculateTradeOutcome(HistoricalTrade trade)
    {
        // Calculate normalized trade outcome (0 = loss, 1 = gain)
        var returnPercent = (trade.ExitPrice - trade.EntryPrice) / trade.EntryPrice;
        return Math.Max(0, Math.Min(1, 0.5 + (double)returnPercent * 5)); // Scale returns
    }

    private void OptimizeFeatureWeights(List<TrainingExample> trainingData)
    {
        // Simple gradient descent optimization
        const double learningRate = 0.01;
        const int iterations = 100;
        
        for (int iter = 0; iter < iterations; iter++)
        {
            var gradients = new Dictionary<string, double>();
            
            foreach (var feature in _featureWeights.Keys)
            {
                gradients[feature] = 0.0;
            }
            
            // Calculate gradients
            foreach (var example in trainingData)
            {
                var predicted = CalculateMLScore(example.Features);
                var error = example.Outcome - predicted;
                
                foreach (var feature in example.Features)
                {
                    if (gradients.ContainsKey(feature.Key))
                    {
                        gradients[feature.Key] += error * feature.Value;
                    }
                }
            }
            
            // Update weights
            foreach (var feature in _featureWeights.Keys.ToList())
            {
                _featureWeights[feature] += learningRate * gradients[feature] / trainingData.Count;
                _featureWeights[feature] = Math.Max(0, Math.Min(1, _featureWeights[feature]));
            }
        }
    }

    private double CalculateModelAccuracy()
    {
        // Placeholder - would calculate based on validation data
        return 0.75; // 75% accuracy
    }

    private MLModelState LoadOrCreateModel()
    {
        try
        {
            if (File.Exists(_modelPath))
            {
                var json = File.ReadAllText(_modelPath);
                var state = JsonSerializer.Deserialize<MLModelState>(json);
                if (state != null)
                {
                    // Load feature weights if available
                    if (state.FeatureWeights?.Any() == true)
                    {
                        foreach (var kv in state.FeatureWeights)
                        {
                            _featureWeights[kv.Key] = kv.Value;
                        }
                    }
                    return state;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error loading ML model, creating new one");
        }
        
        return new MLModelState
        {
            ModelVersion = 1,
            LastTrainingDate = DateTime.UtcNow,
            TrainingDataCount = 0,
            FeatureWeights = _featureWeights
        };
    }

    private void SaveModel()
    {
        try
        {
            _modelState.FeatureWeights = _featureWeights;
            var json = JsonSerializer.Serialize(_modelState, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(_modelPath, json);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving ML model");
        }
    }
}

/// <summary>
/// Enhanced trading signal with ML scoring
/// </summary>
public record MLEnhancedTradingSignal(
    string Symbol,
    decimal Price,
    decimal Atr,
    decimal SixMonthReturn,
    double MLScore,
    double Confidence,
    double RiskAdjustment,
    Dictionary<string, double> Features);

/// <summary>
/// ML model state for persistence
/// </summary>
public class MLModelState
{
    public int ModelVersion { get; set; }
    public DateTime LastTrainingDate { get; set; }
    public int TrainingDataCount { get; set; }
    public Dictionary<string, double>? FeatureWeights { get; set; }
}

/// <summary>
/// Training example for ML model
/// </summary>
public record TrainingExample(Dictionary<string, double> Features, double Outcome);

/// <summary>
/// Historical trade data for training
/// </summary>
public record HistoricalTrade(
    string Symbol,
    DateTime EntryDate,
    decimal EntryPrice,
    DateTime ExitDate,
    decimal ExitPrice,
    decimal Quantity);

/// <summary>
/// ML model performance metrics
/// </summary>
public record MLModelMetrics(
    int ModelVersion,
    DateTime LastTrainingDate,
    int TrainingDataCount,
    double Accuracy,
    Dictionary<string, double> FeatureWeights);
