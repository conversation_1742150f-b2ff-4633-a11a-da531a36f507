using SmaTrendFollower.Models;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced position sizer using Kelly Criterion and dynamic risk adjustment
/// </summary>
public interface IKellyPositionSizer
{
    /// <summary>
    /// Calculates optimal position size using Kelly Criterion
    /// </summary>
    Task<PositionSizing> CalculateKellyPositionAsync(
        TradingSignal signal, 
        decimal accountEquity,
        StrategyPerformanceMetrics? metrics = null);
    
    /// <summary>
    /// Calculates position size with volatility adjustment
    /// </summary>
    Task<PositionSizing> CalculateVolatilityAdjustedPositionAsync(
        TradingSignal signal,
        decimal accountEquity,
        decimal marketVolatility);
}

/// <summary>
/// Kelly Criterion position sizer implementation
/// </summary>
public sealed class KellyPositionSizer : IKellyPositionSizer
{
    private readonly ILogger<KellyPositionSizer> _logger;
    private readonly IAlpacaClientFactory _clientFactory;

    // Default strategy metrics (conservative estimates)
    private static readonly StrategyPerformanceMetrics DefaultMetrics = new(
        WinRate: 0.55m,           // 55% win rate
        AverageWin: 0.08m,        // 8% average win
        AverageLoss: 0.04m,       // 4% average loss
        MaxDrawdown: 0.15m,       // 15% max drawdown
        SharpeRatio: 1.2m         // 1.2 Sharpe ratio
    );

    public KellyPositionSizer(
        ILogger<KellyPositionSizer> logger,
        IAlpacaClientFactory clientFactory)
    {
        _logger = logger;
        _clientFactory = clientFactory;
    }

    public async Task<PositionSizing> CalculateKellyPositionAsync(
        TradingSignal signal, 
        decimal accountEquity,
        StrategyPerformanceMetrics? metrics = null)
    {
        try
        {
            metrics ??= DefaultMetrics;
            
            // Kelly Criterion: f = (bp - q) / b
            // where:
            // f = fraction of capital to wager
            // b = odds received on the wager (average win / average loss)
            // p = probability of winning
            // q = probability of losing (1 - p)
            
            var p = metrics.WinRate;
            var q = 1 - p;
            var b = metrics.AverageWin / metrics.AverageLoss;
            
            var kellyFraction = (b * p - q) / b;
            
            // Apply Kelly fraction limits (never risk more than 25% of capital)
            kellyFraction = Math.Max(0, Math.Min(kellyFraction, 0.25m));
            
            // Apply volatility adjustment
            var volatilityAdjustment = CalculateVolatilityAdjustment(signal.Atr, signal.Price);
            var adjustedFraction = kellyFraction * volatilityAdjustment;
            
            // Calculate position size
            var maxPositionValue = accountEquity * adjustedFraction;
            var quantity = Math.Floor(maxPositionValue / signal.Price);
            
            // Calculate stop loss (2x ATR)
            var stopLossPrice = signal.Price - (signal.Atr * 2);
            
            // Ensure minimum position size
            if (quantity < 1 && maxPositionValue >= signal.Price * 0.1m)
            {
                quantity = 1; // Allow fractional shares for small accounts
            }

            var actualPositionValue = quantity * signal.Price;
            var riskAmount = quantity * (signal.Price - stopLossPrice);
            var riskPercent = accountEquity > 0 ? (riskAmount / accountEquity) * 100 : 0;

            _logger.LogInformation("Kelly position sizing for {Symbol}: " +
                                 "Kelly={Kelly:P2}, Volatility={Vol:P2}, Adjusted={Adj:P2}, " +
                                 "Quantity={Qty}, Risk={Risk:P2}",
                signal.Symbol, kellyFraction, volatilityAdjustment, adjustedFraction,
                quantity, riskPercent / 100);

            return new PositionSizing(quantity, stopLossPrice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating Kelly position for {Symbol}", signal.Symbol);
            
            // Fallback to conservative sizing
            var conservativeQuantity = Math.Floor((accountEquity * 0.01m) / (signal.Atr * 2));
            var conservativeStopLoss = signal.Price - (signal.Atr * 2);
            
            return new PositionSizing(Math.Max(0, conservativeQuantity), conservativeStopLoss);
        }
    }

    public async Task<PositionSizing> CalculateVolatilityAdjustedPositionAsync(
        TradingSignal signal,
        decimal accountEquity,
        decimal marketVolatility)
    {
        try
        {
            // Base risk percentage (2-3% for normal volatility)
            var baseRiskPercent = 0.025m; // 2.5%
            
            // Adjust for market volatility (VIX-based)
            var volatilityAdjustment = CalculateMarketVolatilityAdjustment(marketVolatility);
            var adjustedRiskPercent = baseRiskPercent * volatilityAdjustment;
            
            // Adjust for individual stock volatility
            var stockVolatilityAdjustment = CalculateVolatilityAdjustment(signal.Atr, signal.Price);
            var finalRiskPercent = adjustedRiskPercent * stockVolatilityAdjustment;
            
            // Calculate position size based on risk
            var riskAmount = accountEquity * finalRiskPercent;
            var stopDistance = signal.Atr * 2; // 2x ATR stop
            var quantity = Math.Floor(riskAmount / stopDistance);
            var stopLossPrice = signal.Price - stopDistance;
            
            // Ensure reasonable position size
            var maxPositionValue = accountEquity * 0.20m; // Max 20% of equity per position
            var maxQuantityByValue = Math.Floor(maxPositionValue / signal.Price);
            quantity = Math.Min(quantity, maxQuantityByValue);
            
            var actualRisk = quantity * stopDistance;
            var actualRiskPercent = accountEquity > 0 ? (actualRisk / accountEquity) * 100 : 0;

            _logger.LogInformation("Volatility-adjusted position for {Symbol}: " +
                                 "Market Vol={MarketVol:F1}, Stock Vol={StockVol:P2}, " +
                                 "Risk={Risk:P2}, Quantity={Qty}",
                signal.Symbol, marketVolatility, stockVolatilityAdjustment,
                actualRiskPercent / 100, quantity);

            return new PositionSizing(Math.Max(0, quantity), stopLossPrice);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating volatility-adjusted position for {Symbol}", signal.Symbol);
            
            // Fallback to basic sizing
            var fallbackQuantity = Math.Floor((accountEquity * 0.01m) / (signal.Atr * 2));
            var fallbackStopLoss = signal.Price - (signal.Atr * 2);
            
            return new PositionSizing(Math.Max(0, fallbackQuantity), fallbackStopLoss);
        }
    }

    private decimal CalculateVolatilityAdjustment(decimal atr, decimal price)
    {
        var volatilityRatio = atr / price;
        
        // Reduce position size for high volatility stocks
        return volatilityRatio switch
        {
            <= 0.01m => 1.2m,    // Low volatility: increase size
            <= 0.02m => 1.0m,    // Normal volatility: no adjustment
            <= 0.03m => 0.8m,    // Moderate volatility: reduce size
            <= 0.05m => 0.6m,    // High volatility: significantly reduce
            _ => 0.4m            // Very high volatility: minimal size
        };
    }

    private decimal CalculateMarketVolatilityAdjustment(decimal vix)
    {
        // Adjust position sizing based on VIX levels
        return vix switch
        {
            <= 15m => 1.2m,     // Low VIX: increase risk
            <= 20m => 1.0m,     // Normal VIX: no adjustment
            <= 25m => 0.8m,     // Elevated VIX: reduce risk
            <= 30m => 0.6m,     // High VIX: significantly reduce risk
            _ => 0.4m           // Very high VIX: minimal risk
        };
    }
}

/// <summary>
/// Strategy performance metrics for Kelly Criterion
/// </summary>
public record StrategyPerformanceMetrics(
    decimal WinRate,
    decimal AverageWin,
    decimal AverageLoss,
    decimal MaxDrawdown,
    decimal SharpeRatio);
