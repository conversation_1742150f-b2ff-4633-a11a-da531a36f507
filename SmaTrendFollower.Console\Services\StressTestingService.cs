using SmaTrendFollower.Models;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Services;

/// <summary>
/// Advanced stress testing and scenario analysis for risk management
/// </summary>
public interface IStressTestingService
{
    /// <summary>
    /// Performs comprehensive stress testing on portfolio
    /// </summary>
    Task<StressTestResults> PerformStressTestAsync(PortfolioAllocation portfolio);
    
    /// <summary>
    /// Runs Monte Carlo simulation for portfolio outcomes
    /// </summary>
    Task<MonteCarloResults> RunMonteCarloSimulationAsync(
        PortfolioAllocation portfolio, 
        int simulations = 10000,
        int timeHorizonDays = 252);
    
    /// <summary>
    /// Analyzes portfolio under historical crisis scenarios
    /// </summary>
    Task<CrisisScenarioResults> AnalyzeCrisisScenariosAsync(PortfolioAllocation portfolio);
    
    /// <summary>
    /// Calculates tail risk metrics
    /// </summary>
    Task<TailRiskMetrics> CalculateTailRiskAsync(PortfolioAllocation portfolio);
}

/// <summary>
/// Stress testing service implementation
/// </summary>
public sealed class StressTestingService : IStressTestingService
{
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<StressTestingService> _logger;
    private readonly Random _random;

    // Historical crisis scenarios
    private static readonly List<CrisisScenario> CrisisScenarios = new()
    {
        new("2008 Financial Crisis", new DateTime(2008, 9, 15), new DateTime(2009, 3, 9), -0.57m, 0.80m),
        new("COVID-19 Crash", new DateTime(2020, 2, 19), new DateTime(2020, 3, 23), -0.34m, 0.75m),
        new("Dot-com Crash", new DateTime(2000, 3, 10), new DateTime(2002, 10, 9), -0.49m, 0.60m),
        new("Black Monday", new DateTime(1987, 10, 19), new DateTime(1987, 10, 19), -0.22m, 0.90m),
        new("Flash Crash", new DateTime(2010, 5, 6), new DateTime(2010, 5, 6), -0.09m, 1.50m),
        new("Taper Tantrum", new DateTime(2013, 5, 22), new DateTime(2013, 8, 30), -0.05m, 0.40m)
    };

    public StressTestingService(
        IMarketDataService marketDataService,
        ILogger<StressTestingService> logger)
    {
        _marketDataService = marketDataService;
        _logger = logger;
        _random = new Random();
    }

    public async Task<StressTestResults> PerformStressTestAsync(PortfolioAllocation portfolio)
    {
        try
        {
            _logger.LogInformation("Performing comprehensive stress test on portfolio with {Count} positions",
                portfolio.Positions.Count);

            var stressTests = new List<StressTestScenario>();

            // Market stress scenarios
            stressTests.AddRange(await CreateMarketStressScenariosAsync(portfolio));
            
            // Volatility stress scenarios
            stressTests.AddRange(CreateVolatilityStressScenarios(portfolio));
            
            // Correlation stress scenarios
            stressTests.AddRange(CreateCorrelationStressScenarios(portfolio));
            
            // Liquidity stress scenarios
            stressTests.AddRange(CreateLiquidityStressScenarios(portfolio));

            // Interest rate stress scenarios
            stressTests.AddRange(CreateInterestRateStressScenarios(portfolio));

            // Calculate overall stress test metrics
            var worstCaseScenario = stressTests.OrderBy(s => s.PortfolioReturn).First();
            var averageStressReturn = stressTests.Average(s => s.PortfolioReturn);
            var stressVaR = CalculateStressVaR(stressTests);

            var results = new StressTestResults(
                stressTests,
                worstCaseScenario,
                averageStressReturn,
                stressVaR,
                CalculateStressTestSummary(stressTests));

            _logger.LogInformation("Stress test complete: Worst case={Worst:P2}, " +
                                 "Average stress={Avg:P2}, Stress VaR={VaR:P2}",
                worstCaseScenario.PortfolioReturn, averageStressReturn, stressVaR);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error performing stress test");
            return new StressTestResults(new List<StressTestScenario>(), 
                new StressTestScenario("Error", -0.1m, new Dictionary<string, decimal>()),
                -0.05m, -0.1m, "Error in stress testing");
        }
    }

    public async Task<MonteCarloResults> RunMonteCarloSimulationAsync(
        PortfolioAllocation portfolio, 
        int simulations = 10000,
        int timeHorizonDays = 252)
    {
        try
        {
            _logger.LogInformation("Running Monte Carlo simulation: {Simulations} simulations, " +
                                 "{Days} day horizon", simulations, timeHorizonDays);

            // Get historical data for correlation and volatility estimation
            var historicalData = await GetPortfolioHistoricalDataAsync(portfolio);
            var correlationMatrix = CalculateCorrelationMatrix(historicalData);
            var volatilities = CalculateVolatilities(historicalData);
            var expectedReturns = CalculateExpectedReturns(historicalData);

            var simulationResults = new List<SimulationResult>();

            for (int i = 0; i < simulations; i++)
            {
                var portfolioPath = SimulatePortfolioPath(
                    portfolio, expectedReturns, volatilities, correlationMatrix, timeHorizonDays);
                
                var finalReturn = portfolioPath.Last();
                var maxDrawdown = CalculatePathMaxDrawdown(portfolioPath);
                var volatility = CalculatePathVolatility(portfolioPath);

                simulationResults.Add(new SimulationResult(finalReturn, maxDrawdown, volatility, portfolioPath));
            }

            // Calculate Monte Carlo statistics
            var returns = simulationResults.Select(r => r.FinalReturn).OrderBy(r => r).ToList();
            var drawdowns = simulationResults.Select(r => r.MaxDrawdown).ToList();

            var results = new MonteCarloResults(
                simulations,
                timeHorizonDays,
                returns.Average(),
                returns.Skip((int)(simulations * 0.05)).First(), // 5th percentile
                returns.Skip((int)(simulations * 0.95)).First(), // 95th percentile
                drawdowns.Average(),
                drawdowns.Max(),
                CalculateProbabilityOfLoss(returns),
                simulationResults);

            _logger.LogInformation("Monte Carlo complete: Mean return={Mean:P2}, " +
                                 "5th percentile={P5:P2}, 95th percentile={P95:P2}",
                results.MeanReturn, results.Percentile5, results.Percentile95);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running Monte Carlo simulation");
            return new MonteCarloResults(0, 0, 0, 0, 0, 0, 0, 0, new List<SimulationResult>());
        }
    }

    public async Task<CrisisScenarioResults> AnalyzeCrisisScenariosAsync(PortfolioAllocation portfolio)
    {
        try
        {
            _logger.LogInformation("Analyzing portfolio under {Count} historical crisis scenarios",
                CrisisScenarios.Count);

            var scenarioResults = new List<CrisisScenarioResult>();

            foreach (var crisis in CrisisScenarios)
            {
                var portfolioImpact = CalculateCrisisImpact(portfolio, crisis);
                var recoveryTime = EstimateRecoveryTime(crisis, portfolioImpact);
                
                scenarioResults.Add(new CrisisScenarioResult(
                    crisis.Name, crisis.StartDate, crisis.EndDate,
                    portfolioImpact, recoveryTime, crisis.MarketDecline, crisis.VolatilitySpike));
            }

            var worstCrisis = scenarioResults.OrderBy(r => r.PortfolioImpact).First();
            var averageImpact = scenarioResults.Average(r => r.PortfolioImpact);
            var averageRecovery = scenarioResults.Average(r => r.EstimatedRecoveryDays);

            var results = new CrisisScenarioResults(
                scenarioResults, worstCrisis, averageImpact, averageRecovery);

            _logger.LogInformation("Crisis analysis complete: Worst crisis impact={Worst:P2}, " +
                                 "Average impact={Avg:P2}", worstCrisis.PortfolioImpact, averageImpact);

            return results;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing crisis scenarios");
            return new CrisisScenarioResults(new List<CrisisScenarioResult>(), 
                new CrisisScenarioResult("Error", DateTime.Now, DateTime.Now, -0.1m, 100, -0.1m, 0.5m),
                -0.05m, 100);
        }
    }

    public async Task<TailRiskMetrics> CalculateTailRiskAsync(PortfolioAllocation portfolio)
    {
        try
        {
            // Run Monte Carlo for tail risk calculation
            var monteCarloResults = await RunMonteCarloSimulationAsync(portfolio, 50000, 252);
            var returns = monteCarloResults.SimulationResults.Select(r => r.FinalReturn).OrderBy(r => r).ToList();

            // Calculate various tail risk metrics
            var var95 = returns.Skip((int)(returns.Count * 0.05)).First();
            var var99 = returns.Skip((int)(returns.Count * 0.01)).First();
            var var999 = returns.Skip((int)(returns.Count * 0.001)).First();

            // Expected Shortfall (Conditional VaR)
            var es95 = returns.Take((int)(returns.Count * 0.05)).Average();
            var es99 = returns.Take((int)(returns.Count * 0.01)).Average();

            // Tail ratio
            var tailRatio = Math.Abs(var95) / Math.Abs(var99);

            // Maximum loss probability
            var maxLossProbability = returns.Count(r => r < -0.5m) / (double)returns.Count;

            // Tail dependency
            var tailDependency = CalculateTailDependency(portfolio);

            return new TailRiskMetrics(
                var95, var99, var999, es95, es99, (double)tailRatio, maxLossProbability, (decimal)tailDependency);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating tail risk metrics");
            return new TailRiskMetrics(-0.05m, -0.1m, -0.2m, -0.08m, -0.15m, 0.5, 0.01, 0.3m);
        }
    }

    private async Task<List<StressTestScenario>> CreateMarketStressScenariosAsync(PortfolioAllocation portfolio)
    {
        var scenarios = new List<StressTestScenario>();

        // Market decline scenarios
        var marketDeclines = new[] { -0.05m, -0.10m, -0.20m, -0.30m, -0.50m };
        
        foreach (var decline in marketDeclines)
        {
            var positionImpacts = new Dictionary<string, decimal>();
            var portfolioReturn = 0m;

            foreach (var position in portfolio.Positions)
            {
                // Assume beta of 1.0 for simplicity (would calculate actual beta in production)
                var positionImpact = decline * 1.0m;
                positionImpacts[position.Symbol] = positionImpact;
                portfolioReturn += positionImpact * position.Weight;
            }

            scenarios.Add(new StressTestScenario(
                $"Market Decline {decline:P0}", portfolioReturn, positionImpacts));
        }

        return scenarios;
    }

    private List<StressTestScenario> CreateVolatilityStressScenarios(PortfolioAllocation portfolio)
    {
        var scenarios = new List<StressTestScenario>();
        var volSpikes = new[] { 1.5m, 2.0m, 3.0m, 5.0m };

        foreach (var spike in volSpikes)
        {
            var positionImpacts = new Dictionary<string, decimal>();
            var portfolioReturn = 0m;

            foreach (var position in portfolio.Positions)
            {
                // Higher volatility typically leads to negative returns in the short term
                var positionImpact = -0.02m * spike; // 2% decline per volatility multiple
                positionImpacts[position.Symbol] = positionImpact;
                portfolioReturn += positionImpact * position.Weight;
            }

            scenarios.Add(new StressTestScenario(
                $"Volatility Spike {spike}x", portfolioReturn, positionImpacts));
        }

        return scenarios;
    }

    private List<StressTestScenario> CreateCorrelationStressScenarios(PortfolioAllocation portfolio)
    {
        var scenarios = new List<StressTestScenario>();

        // High correlation scenario (all positions move together)
        var highCorrImpacts = new Dictionary<string, decimal>();
        var highCorrReturn = 0m;
        var uniformDecline = -0.15m;

        foreach (var position in portfolio.Positions)
        {
            highCorrImpacts[position.Symbol] = uniformDecline;
            highCorrReturn += uniformDecline * position.Weight;
        }

        scenarios.Add(new StressTestScenario(
            "High Correlation Crisis", highCorrReturn, highCorrImpacts));

        return scenarios;
    }

    private List<StressTestScenario> CreateLiquidityStressScenarios(PortfolioAllocation portfolio)
    {
        var scenarios = new List<StressTestScenario>();

        // Liquidity crisis scenario
        var liquidityImpacts = new Dictionary<string, decimal>();
        var liquidityReturn = 0m;

        foreach (var position in portfolio.Positions)
        {
            // Assume larger positions are harder to liquidate
            var liquidityImpact = -0.05m * Math.Min(2.0m, (decimal)position.Weight * 10);
            liquidityImpacts[position.Symbol] = liquidityImpact;
            liquidityReturn += liquidityImpact * position.Weight;
        }

        scenarios.Add(new StressTestScenario(
            "Liquidity Crisis", liquidityReturn, liquidityImpacts));

        return scenarios;
    }

    private List<StressTestScenario> CreateInterestRateStressScenarios(PortfolioAllocation portfolio)
    {
        var scenarios = new List<StressTestScenario>();
        var rateChanges = new[] { 1.0m, 2.0m, 3.0m }; // Percentage point changes

        foreach (var rateChange in rateChanges)
        {
            var rateImpacts = new Dictionary<string, decimal>();
            var rateReturn = 0m;

            foreach (var position in portfolio.Positions)
            {
                // Interest rate sensitivity (simplified)
                var duration = 5.0m; // Assume 5-year duration
                var rateImpact = -duration * rateChange / 100m;
                rateImpacts[position.Symbol] = rateImpact;
                rateReturn += rateImpact * position.Weight;
            }

            scenarios.Add(new StressTestScenario(
                $"Interest Rate +{rateChange}%", rateReturn, rateImpacts));
        }

        return scenarios;
    }

    private async Task<Dictionary<string, List<decimal>>> GetPortfolioHistoricalDataAsync(PortfolioAllocation portfolio)
    {
        var historicalData = new Dictionary<string, List<decimal>>();
        var endDate = DateTime.UtcNow;
        var startDate = endDate.AddDays(-500); // ~2 years of data

        foreach (var position in portfolio.Positions)
        {
            try
            {
                var barsResponse = await _marketDataService.GetStockBarsAsync(position.Symbol, startDate, endDate);
                var bars = barsResponse.Items.ToList();
                
                var returns = new List<decimal>();
                for (int i = 1; i < bars.Count; i++)
                {
                    var ret = (bars[i].Close - bars[i - 1].Close) / bars[i - 1].Close;
                    returns.Add(ret);
                }
                
                historicalData[position.Symbol] = returns;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error getting historical data for {Symbol}", position.Symbol);
                // Use default returns if data unavailable
                historicalData[position.Symbol] = Enumerable.Repeat(0.001m, 252).ToList();
            }
        }

        return historicalData;
    }

    private decimal[,] CalculateCorrelationMatrix(Dictionary<string, List<decimal>> historicalData)
    {
        var symbols = historicalData.Keys.ToList();
        var n = symbols.Count;
        var correlationMatrix = new decimal[n, n];

        for (int i = 0; i < n; i++)
        {
            for (int j = 0; j < n; j++)
            {
                if (i == j)
                {
                    correlationMatrix[i, j] = 1.0m;
                }
                else
                {
                    var correlation = CalculateCorrelation(
                        historicalData[symbols[i]], historicalData[symbols[j]]);
                    correlationMatrix[i, j] = correlation;
                }
            }
        }

        return correlationMatrix;
    }

    private decimal CalculateCorrelation(List<decimal> returns1, List<decimal> returns2)
    {
        var n = Math.Min(returns1.Count, returns2.Count);
        if (n < 2) return 0.5m;

        var mean1 = returns1.Take(n).Average();
        var mean2 = returns2.Take(n).Average();

        var numerator = 0m;
        var sum1 = 0m;
        var sum2 = 0m;

        for (int i = 0; i < n; i++)
        {
            var diff1 = returns1[i] - mean1;
            var diff2 = returns2[i] - mean2;
            
            numerator += diff1 * diff2;
            sum1 += diff1 * diff1;
            sum2 += diff2 * diff2;
        }

        var denominator = (decimal)Math.Sqrt((double)(sum1 * sum2));
        return denominator > 0 ? numerator / denominator : 0m;
    }

    private Dictionary<string, decimal> CalculateVolatilities(Dictionary<string, List<decimal>> historicalData)
    {
        var volatilities = new Dictionary<string, decimal>();

        foreach (var kvp in historicalData)
        {
            var returns = kvp.Value;
            var mean = returns.Average();
            var variance = returns.Average(r => (r - mean) * (r - mean));
            var volatility = (decimal)Math.Sqrt((double)variance) * (decimal)Math.Sqrt(252); // Annualized
            
            volatilities[kvp.Key] = volatility;
        }

        return volatilities;
    }

    private Dictionary<string, decimal> CalculateExpectedReturns(Dictionary<string, List<decimal>> historicalData)
    {
        var expectedReturns = new Dictionary<string, decimal>();

        foreach (var kvp in historicalData)
        {
            var annualizedReturn = kvp.Value.Average() * 252; // Annualized
            expectedReturns[kvp.Key] = annualizedReturn;
        }

        return expectedReturns;
    }

    private List<decimal> SimulatePortfolioPath(
        PortfolioAllocation portfolio,
        Dictionary<string, decimal> expectedReturns,
        Dictionary<string, decimal> volatilities,
        decimal[,] correlationMatrix,
        int timeHorizonDays)
    {
        var portfolioPath = new List<decimal> { 0m }; // Start at 0% return
        var symbols = portfolio.Positions.Select(p => p.Symbol).ToList();

        for (int day = 0; day < timeHorizonDays; day++)
        {
            var dailyReturn = 0m;

            for (int i = 0; i < symbols.Count; i++)
            {
                var symbol = symbols[i];
                var position = portfolio.Positions.First(p => p.Symbol == symbol);
                
                // Generate correlated random return
                var randomReturn = GenerateRandomReturn(
                    expectedReturns[symbol] / 252, // Daily expected return
                    volatilities[symbol] / (decimal)Math.Sqrt(252)); // Daily volatility
                
                dailyReturn += randomReturn * position.Weight;
            }

            var cumulativeReturn = portfolioPath.Last() + dailyReturn;
            portfolioPath.Add(cumulativeReturn);
        }

        return portfolioPath;
    }

    private decimal GenerateRandomReturn(decimal expectedReturn, decimal volatility)
    {
        // Box-Muller transformation for normal distribution
        var u1 = _random.NextDouble();
        var u2 = _random.NextDouble();
        var z = Math.Sqrt(-2 * Math.Log(u1)) * Math.Cos(2 * Math.PI * u2);
        
        return expectedReturn + volatility * (decimal)z;
    }

    private decimal CalculatePathMaxDrawdown(List<decimal> path)
    {
        var maxDrawdown = 0m;
        var peak = 0m;

        foreach (var value in path)
        {
            if (value > peak)
                peak = value;
            
            var drawdown = peak - value;
            if (drawdown > maxDrawdown)
                maxDrawdown = drawdown;
        }

        return maxDrawdown;
    }

    private decimal CalculatePathVolatility(List<decimal> path)
    {
        if (path.Count < 2) return 0m;

        var returns = new List<decimal>();
        for (int i = 1; i < path.Count; i++)
        {
            returns.Add(path[i] - path[i - 1]);
        }

        var mean = returns.Average();
        var variance = returns.Average(r => (r - mean) * (r - mean));
        return (decimal)Math.Sqrt((double)variance) * (decimal)Math.Sqrt(252);
    }

    private decimal CalculateStressVaR(List<StressTestScenario> scenarios)
    {
        var returns = scenarios.Select(s => s.PortfolioReturn).OrderBy(r => r).ToList();
        var index = (int)(returns.Count * 0.05); // 5th percentile
        return returns[Math.Max(0, index)];
    }

    private string CalculateStressTestSummary(List<StressTestScenario> scenarios)
    {
        var failedScenarios = scenarios.Count(s => s.PortfolioReturn < -0.1m);
        var severeScenarios = scenarios.Count(s => s.PortfolioReturn < -0.2m);
        
        return $"Failed scenarios: {failedScenarios}/{scenarios.Count}, " +
               $"Severe scenarios: {severeScenarios}/{scenarios.Count}";
    }

    private decimal CalculateCrisisImpact(PortfolioAllocation portfolio, CrisisScenario crisis)
    {
        // Simplified crisis impact calculation
        // In production, would use historical correlations and factor models
        var baseImpact = crisis.MarketDecline * 0.8m; // Assume 80% correlation with market
        var volatilityAdjustment = crisis.VolatilitySpike * 0.1m; // Additional impact from volatility
        
        return baseImpact - volatilityAdjustment;
    }

    private int EstimateRecoveryTime(CrisisScenario crisis, decimal portfolioImpact)
    {
        // Simplified recovery time estimation
        var baseDays = (crisis.EndDate - crisis.StartDate).Days;
        var impactMultiplier = Math.Abs(portfolioImpact) / Math.Abs(crisis.MarketDecline);
        
        return (int)(baseDays * (double)impactMultiplier);
    }

    private decimal CalculateProbabilityOfLoss(List<decimal> returns)
    {
        var lossCount = returns.Count(r => r < 0);
        return (decimal)lossCount / returns.Count;
    }

    private decimal CalculateTailDependency(PortfolioAllocation portfolio)
    {
        // Simplified tail dependency calculation
        // In production, would use copula models
        return 0.3m; // 30% tail dependency
    }
}

// Data structures for stress testing
public record StressTestResults(
    List<StressTestScenario> Scenarios,
    StressTestScenario WorstCaseScenario,
    decimal AverageStressReturn,
    decimal StressVaR,
    string Summary);

public record StressTestScenario(
    string Name,
    decimal PortfolioReturn,
    Dictionary<string, decimal> PositionImpacts);

public record MonteCarloResults(
    int Simulations,
    int TimeHorizonDays,
    decimal MeanReturn,
    decimal Percentile5,
    decimal Percentile95,
    decimal AverageMaxDrawdown,
    decimal WorstMaxDrawdown,
    decimal ProbabilityOfLoss,
    List<SimulationResult> SimulationResults);

public record SimulationResult(
    decimal FinalReturn,
    decimal MaxDrawdown,
    decimal Volatility,
    List<decimal> Path);

public record CrisisScenarioResults(
    List<CrisisScenarioResult> Scenarios,
    CrisisScenarioResult WorstCrisis,
    decimal AverageImpact,
    double AverageRecoveryDays);

public record CrisisScenarioResult(
    string CrisisName,
    DateTime StartDate,
    DateTime EndDate,
    decimal PortfolioImpact,
    double EstimatedRecoveryDays,
    decimal MarketDecline,
    decimal VolatilitySpike);

public record CrisisScenario(
    string Name,
    DateTime StartDate,
    DateTime EndDate,
    decimal MarketDecline,
    decimal VolatilitySpike);

public record TailRiskMetrics(
    decimal VaR95,
    decimal VaR99,
    decimal VaR999,
    decimal ExpectedShortfall95,
    decimal ExpectedShortfall99,
    double TailRatio,
    double MaxLossProbability,
    decimal TailDependency);
